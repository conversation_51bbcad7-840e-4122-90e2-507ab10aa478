package co.metode.hamim;

import android.app.Activity;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.ConnectivityManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.cek_hafalan.ItemModel;
import co.metode.hamim.cek_hafalan.database.HafalanDatabase;
import co.metode.hamim.cek_hafalan.database.OfflineActionEntity;
import co.metode.hamim.complete_surat.CompleteSurat;
import co.metode.hamim.surat.audioFiles.database.AudioFilesEntity;
import co.metode.hamim.surat.detailSurat.database.SuratDetailDatabase;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class AdapterChallenge extends RecyclerView.Adapter<AdapterChallenge.AdapterHolder> {
    private Context context;
    public ApiInterface apiInterface;
    private List<ItemModel> itemList;
    private final ArrayList<String> arrayListNamaSurat;
    private final ArrayList<String> arrayListUrl;
    private String idUser;
    private String namaJuz;
    private OnHafalStatusChangedListener listener;

    private final SuratDetailDatabase suratDetailDatabase;
    private final Executor executor;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    private HafalanDatabase database;
    private ExecutorService executorService;

    // Assets bitmaps
    private Bitmap ayatButtonBitmap;
    private Bitmap sambungAyatButtonBitmap;

    public AdapterChallenge(Context context, List<ItemModel> itemList, ArrayList<String> arrayListNamaSurat, ArrayList<String> arrayListUrl, String idUser, String namaJuz) {
        this.context = context;
        this.itemList = itemList;
        this.arrayListNamaSurat = arrayListNamaSurat;
        this.arrayListUrl = arrayListUrl;
        this.idUser = idUser;
        this.suratDetailDatabase = SuratDetailDatabase.getDatabase(context);
        this.executor = Executors.newSingleThreadExecutor();

        this.database = HafalanDatabase.getDatabase(context);
        this.executorService = Executors.newSingleThreadExecutor();
        this.namaJuz = namaJuz;

        initializeLists();
        loadAssetImages();
    }

    private void initializeLists() {
        for (ItemModel item : itemList) {
            arrayListNamaSurat.add(item.getJumlahAyat());
            arrayListUrl.add(item.getUrl_audio());
        }
    }

    private void loadAssetImages() {
        try {
            AssetManager assetManager = context.getAssets();

            // Load button-ayat.png
            java.io.InputStream ayatInputStream = assetManager.open("button-ayat.png");
            ayatButtonBitmap = BitmapFactory.decodeStream(ayatInputStream);
            ayatInputStream.close();

            // Load button-sambung-ayat.png
            java.io.InputStream sambungInputStream = assetManager.open("button-sambung-ayat.png");
            sambungAyatButtonBitmap = BitmapFactory.decodeStream(sambungInputStream);
            sambungInputStream.close();

        } catch (java.io.IOException e) {
            Log.e("AdapterChallenge", "Failed to load asset images", e);
            // Fallback will be handled in bindItem method
        }
    }

    public interface OnHafalStatusChangedListener {
        void onHafalStatusChanged(boolean isHafal);
    }

    public interface OnItemClickListener {
        void onItemClick(int position);
    }

    private OnItemClickListener onItemClickListener;

    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }

    public void setOnHafalStatusChangedListener(OnHafalStatusChangedListener listener) {
        this.listener = listener;
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    @NonNull
    @Override
    public AdapterHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.daftar_surat_challenge, parent, false);
        return new AdapterHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull AdapterHolder holder, int position) {
        final ItemModel item = itemList.get(position);
        String id_surat = item.getIdSurat();
        String id_detail_surat = item.getId_detail_surat();
        String title = item.getNamaSurat();
        String jumlah_ayat = item.getJumlah_ayat_potong();
        int isHafal = item.getIs_hafal();

        bindItem(holder, item, position, title, jumlah_ayat, isHafal, id_surat, id_detail_surat);

        // Set click listeners for the level items
        final int pos = position;
        View.OnClickListener clickListener = v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(pos);
            }
        };

        // Apply click listener to both left and right buttons
        holder.btn.setOnClickListener(clickListener);
        holder.btn2.setOnClickListener(clickListener);

        // Handle floating "Mulai" text
        handleFloatingMulaiText(holder, position);
    }

    private void bindItem(@NonNull AdapterHolder holder, ItemModel item, int position, String title, String jmlAyat, int isHafal, String id_surat, String id_detail_surat) {
        String number = Integer.toString(position + 1);
        boolean isEven = position % 2 == 0;
        boolean shouldEnable = true;

        // Dummy logic: Level 2, 5, dan 8 adalah level "Sambung Ayat"
        boolean isSlicingLevel = (position == 1 || position == 4 || position == 7);

        // Modified logic: Enable button if it's the current active level or if all previous levels are completed
        int nextActiveLevel = findNextActiveLevel();

        // Enable button if:
        // 1. It's the current active level (the one that should be accessible next)
        // 2. OR all previous levels are completed
        if (position == nextActiveLevel) {
            // This is the current active level - always enable it
            shouldEnable = true;
            Log.d("AdapterChallenge", "Position " + position + " enabled as current active level");
        } else if (position < nextActiveLevel) {
            // This is a previous level - check if it's completed
            shouldEnable = (itemList.get(position).getIs_hafal() == 100);
            Log.d("AdapterChallenge", "Position " + position + " (previous level) enabled: " + shouldEnable + " (hafal: " + itemList.get(position).getIs_hafal() + ")");
        } else {
            // This is a future level - check if all previous levels are completed
            shouldEnable = true;
            for (int i = 0; i < position; i++) {
                if (itemList.get(i).getIs_hafal() != 100) {
                    shouldEnable = false;
                    Log.d("AdapterChallenge", "Position " + position + " disabled because position " + i + " is not hafal (status: " + itemList.get(i).getIs_hafal() + ")");
                    break;
                }
            }
            if (shouldEnable) {
                Log.d("AdapterChallenge", "Position " + position + " (future level) enabled - all previous levels completed");
            }
        }

        if (isEven) {
            holder.layout_right.setVisibility(View.GONE);

            if (isSlicingLevel) {
                // Setup untuk level Sambung Ayat
                holder.urutanAudio.setText("Audio " + number);
                holder.ayat.setText("Sambung " + title);
                if (sambungAyatButtonBitmap != null) {
                    holder.btn.setImageBitmap(sambungAyatButtonBitmap);
                } else {
                    // Fallback to drawable
                    holder.btn.setImageResource(R.drawable.circle_orange);
                }
                holder.ic_tandai_hafal_right.setVisibility(View.GONE);
                cekHafal(holder, true, isHafal);
            } else {
                // Setup untuk level biasa
                holder.urutanAudio.setText("Audio " + number);
                holder.ayat.setText(title);
                if (ayatButtonBitmap != null) {
                    holder.btn.setImageBitmap(ayatButtonBitmap);
                } else {
                    // Fallback to drawable
                    holder.btn.setImageResource(R.drawable.circle_orange);
                }
                holder.ic_tandai_hafal_right.setVisibility(View.GONE);
                cekHafal(holder, true, isHafal);
            }

            holder.btn.setEnabled(shouldEnable);
            holder.btn.setAlpha(shouldEnable ? 1.0f : 0.5f);
            boolean finalShouldEnable = shouldEnable;
            holder.btn.setOnClickListener(v -> {
                Log.d("AdapterChallenge", "Button clicked at position " + position + ", shouldEnable: " + finalShouldEnable + ", isSlicingLevel: " + isSlicingLevel);

                if (finalShouldEnable) {
                    if (isSlicingLevel) {
                        // Launch SambungAyat activity untuk level slicing
                        Log.d("AdapterChallenge", "Launching SambungAyat activity for position " + position);
                        launchSambungAyatActivity(title, position);
                    } else {
                        // Launch audio player untuk level biasa
                        Log.d("AdapterChallenge", "Launching audio player for position " + position);
                        executor.execute(() -> {
                            try {
                                AudioFilesEntity audioFile = suratDetailDatabase.audioFileDao().getAudioFile(item.getId_detail_surat());
                                mainHandler.post(() -> {
                                    if (audioFile != null && audioFile.isDownloaded()) {
                                        ArrayList<String> localPaths = new ArrayList<>();
                                        localPaths.add(audioFile.getLocalPath());

                                        launchPlayActivity(holder, item, position, audioFile.getLocalPath());
                                    } else {
                                        showDownloadDialog(holder, item, position);
                                    }
                                });
                            } catch (Exception e) {
                                mainHandler.post(() -> {
                                    Toast.makeText(context, "Error checking audio: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                                });
                            }
                        });
                    }
                } else {
                    Log.d("AdapterChallenge", "Button click blocked at position " + position + " - previous levels not completed");
                    Toast.makeText(context, "Selesaikan dulu hafalan sebelumnya", Toast.LENGTH_SHORT).show();
                }
            });

            if (!shouldEnable) {
                holder.tandai_hafal_left.setOnClickListener(v -> {
                    Toast.makeText(context, "Selesaikan dulu hafalan sebelumnya", Toast.LENGTH_SHORT).show();
                });
            } else {
                tandaiHafal(holder, true, holder, id_surat, id_detail_surat);
            }
            holder.tandai_hafal_left.setAlpha(shouldEnable ? 1.0f : 0.5f);
        } else {
            holder.layout_left.setVisibility(View.GONE);

            if (isSlicingLevel) {
                // Setup untuk level Sambung Ayat
                if (sambungAyatButtonBitmap != null) {
                    holder.btn2.setImageBitmap(sambungAyatButtonBitmap);
                } else {
                    // Fallback to drawable
                    holder.btn2.setImageResource(R.drawable.circle_orange);
                }
                holder.urutanAudio2.setText("Audio " + number);
                holder.ayat2.setText("Sambung " + title);
                holder.ic_tandai_hafal_left.setVisibility(View.GONE);
                cekHafal(holder, false, isHafal);
            } else {
                // Setup untuk level biasa
                if (ayatButtonBitmap != null) {
                    holder.btn2.setImageBitmap(ayatButtonBitmap);
                } else {
                    // Fallback to drawable
                    holder.btn2.setImageResource(R.drawable.circle_orange);
                }
                holder.urutanAudio2.setText("Audio " + number);
                holder.ayat2.setText(title);
                holder.ic_tandai_hafal_left.setVisibility(View.GONE);
                cekHafal(holder, false, isHafal);
            }

            holder.btn2.setEnabled(shouldEnable);
            holder.btn2.setAlpha(shouldEnable ? 1.0f : 0.5f);
            boolean finalShouldEnable1 = shouldEnable;
            holder.btn2.setOnClickListener(v -> {
                Log.d("AdapterChallenge", "Button2 clicked at position " + position + ", shouldEnable: " + finalShouldEnable1 + ", isSlicingLevel: " + isSlicingLevel);

                if (finalShouldEnable1) {
                    if (isSlicingLevel) {
                        // Launch SambungAyat activity untuk level slicing
                        Log.d("AdapterChallenge", "Launching SambungAyat activity for position " + position);
                        launchSambungAyatActivity(title, position);
                    } else {
                        // Launch audio player untuk level biasa
                        Log.d("AdapterChallenge", "Launching audio player for position " + position);
                        executor.execute(() -> {
                            try {
                                AudioFilesEntity audioFile = suratDetailDatabase.audioFileDao().getAudioFile(item.getId_detail_surat());
                                mainHandler.post(() -> {
                                    if (audioFile != null && audioFile.isDownloaded()) {
                                        ArrayList<String> localPaths = new ArrayList<>();
                                        localPaths.add(audioFile.getLocalPath());
                                        launchPlayActivity(holder, item, position, audioFile.getLocalPath());
                                    } else {
                                        showDownloadDialog(holder, item, position);
                                    }
                                });
                            } catch (Exception e) {
                                mainHandler.post(() -> {
                                    Toast.makeText(context, "Error checking audio: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                                });
                            }
                        });
                    }
                } else {
                    Log.d("AdapterChallenge", "Button2 click blocked at position " + position + " - previous levels not completed");
                    Toast.makeText(context, "Selesaikan dulu hafalan sebelumnya", Toast.LENGTH_SHORT).show();
                }
            });

            if (!shouldEnable) {
                holder.tandai_hafal_right.setOnClickListener(v -> {
                    Toast.makeText(context, "Selesaikan dulu hafalan sebelumnya", Toast.LENGTH_SHORT).show();
                });
            } else {
                tandaiHafal(holder, false, holder, id_surat, id_detail_surat);
            }
            holder.tandai_hafal_right.setAlpha(shouldEnable ? 1.0f : 0.5f);
        }
    }

    /**
     * Method untuk loading bitmap dari assets dengan lebih reliable
     */
    private Bitmap loadBitmapFromAsset(String fileName) {
        AssetManager assetManager = context.getAssets();
        InputStream stream = null;
        try {
            stream = assetManager.open(fileName);
            return BitmapFactory.decodeStream(stream);
        } catch (IOException e) {
            Log.e("AdapterChallenge", "Error loading asset: " + fileName, e);
            return null;
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e) {
                    Log.e("AdapterChallenge", "Error closing stream", e);
                }
            }
        }
    }

    // Pop-up Mulai bitmap (cached untuk performa)
    private Bitmap popUpMulaiBitmap = null;

    /**
     * Menangani tampilan floating text "Mulai"
     * Text akan muncul di level yang harus diselesaikan user selanjutnya
     * Menggunakan gambar dari assets
     */
    private void handleFloatingMulaiText(@NonNull AdapterHolder holder, int position) {
        try {
            // Cari posisi level yang belum diselesaikan (level aktif)
            int nextLevelPosition = findNextActiveLevel();

            // Tampilkan "Mulai" button hanya untuk level aktif
            if (position == nextLevelPosition) {
                // Gunakan holder.mulaiButton yang sudah ada di layout
                if (holder.mulaiButton != null) {
                    // Tampilkan mulai button
                    holder.mulaiButton.setVisibility(View.VISIBLE);

                    // Set gambar dari assets jika tersedia
                    if (popUpMulaiBitmap == null) {
                        // Load gambar dari assets
                        Log.d("AdapterChallenge", "Loading pop_up_mulai.png from assets...");
                        popUpMulaiBitmap = loadBitmapFromAsset("pop_up_mulai.png");

                        if (popUpMulaiBitmap != null) {
                            Log.d("AdapterChallenge", "Successfully loaded pop_up_mulai.png: " +
                                  popUpMulaiBitmap.getWidth() + "x" + popUpMulaiBitmap.getHeight());
                        } else {
                            Log.e("AdapterChallenge", "Failed to load pop_up_mulai.png");
                        }
                    }

                    // Set mulai button image
                    if (popUpMulaiBitmap != null) {
                        holder.mulaiButton.setImageBitmap(popUpMulaiBitmap);
                        holder.mulaiButton.setBackgroundColor(Color.TRANSPARENT);
                    } else {
                        // Fallback - gunakan drawable default
                        holder.mulaiButton.setImageResource(R.drawable.circle_orange);
                        holder.mulaiButton.setBackgroundColor(Color.TRANSPARENT);
                    }

                    // Get reference to the button that should have the Mulai popup above it
                    boolean isEven = position % 2 == 0;
                    View buttonView = isEven ? holder.btn : holder.btn2;

                    // We need to position the Mulai button after layout is complete
                    // Add a small delay to ensure layout is fully complete
                    buttonView.post(() -> {
                        // Additional delay to ensure all measurements are ready
                        buttonView.postDelayed(() -> {
                        try {
                            // Get button position using global coordinates for consistency
                            int[] buttonLocation = new int[2];
                            int[] itemLocation = new int[2];
                            buttonView.getLocationInWindow(buttonLocation);
                            holder.itemView.getLocationInWindow(itemLocation);

                            // Calculate relative position within the item view
                            int buttonRelativeX = buttonLocation[0] - itemLocation[0];
                            int buttonRelativeY = buttonLocation[1] - itemLocation[1];
                            int buttonWidth = buttonView.getWidth();
                            int buttonHeight = buttonView.getHeight();

                            // Create layout params for absolute positioning within FrameLayout
                            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                                FrameLayout.LayoutParams.WRAP_CONTENT,
                                FrameLayout.LayoutParams.WRAP_CONTENT
                            );

                            // Get Mulai button dimensions from asset or use defaults - make it smaller and more appropriate
                            int mulaiButtonWidth = popUpMulaiBitmap != null ?
                                (int)(popUpMulaiBitmap.getWidth() * 2.0f) : 120; // Reduced from 2.3x to 1.5x
                            int mulaiButtonHeight = popUpMulaiBitmap != null ?
                                (int)(popUpMulaiBitmap.getHeight() * 2.0f) : 65; // Reduced from 2.3x to 1.5x

                            // Calculate button center - this should be consistent for both sides
                            int buttonCenterX = buttonRelativeX + (buttonWidth / 2);

                            // Center horizontally above the button
                            params.leftMargin = buttonCenterX - (mulaiButtonWidth / 2);

                            // Position above the button with a larger gap to avoid covering "Tandai Hafal" area
                            int gap = 45; // Increased gap from 15 to 45 to avoid covering buttons and text
                            params.topMargin = buttonRelativeY - mulaiButtonHeight - gap;

                            // Ensure the button stays within bounds and doesn't cover important elements
                            if (params.leftMargin < 0) {
                                params.leftMargin = 10;
                            }
                            if (params.topMargin < 0) {
                                params.topMargin = 20; // Increased minimum top margin to avoid covering elements
                            }

                            // Additional safety check: ensure minimum distance from item edges
                            int itemWidth = holder.itemView.getWidth();
                            if (params.leftMargin + mulaiButtonWidth > itemWidth - 10) {
                                params.leftMargin = itemWidth - mulaiButtonWidth - 10;
                            }

                            // Set the ImageView size to match our calculated dimensions
                            params.width = mulaiButtonWidth;
                            params.height = mulaiButtonHeight;

                            // Apply the layout params
                            holder.mulaiButton.setLayoutParams(params);

                            Log.d("AdapterChallenge", "Positioned Mulai button at: x=" + params.leftMargin +
                                  ", y=" + params.topMargin + " above button at: x=" + buttonRelativeX + ", y=" + buttonRelativeY +
                                  " (button size: " + buttonWidth + "x" + buttonHeight +
                                  ", mulai size: " + mulaiButtonWidth + "x" + mulaiButtonHeight +
                                  ", isEven: " + isEven + ", buttonCenterX: " + buttonCenterX + ")");

                        } catch (Exception e) {
                            Log.e("AdapterChallenge", "Error positioning Mulai button", e);

                            // Fallback positioning - simple approach with better centering
                            FrameLayout.LayoutParams fallbackParams = new FrameLayout.LayoutParams(
                                FrameLayout.LayoutParams.WRAP_CONTENT,
                                FrameLayout.LayoutParams.WRAP_CONTENT
                            );

                            if (isEven) {
                                // Position above left button - use absolute positioning with better spacing
                                fallbackParams.leftMargin = 50; // Adjust based on your layout
                                fallbackParams.topMargin = 15; // Increased from 5 to 15 for better spacing
                            } else {
                                // Position above right button - use absolute positioning with better spacing
                                fallbackParams.leftMargin = holder.itemView.getWidth() - 150; // Reduced from 170 to 150 for smaller button
                                fallbackParams.topMargin = 15; // Increased from 5 to 15 for better spacing
                            }

                            holder.mulaiButton.setLayoutParams(fallbackParams);
                        }
                        }, 50); // 50ms delay to ensure layout is complete
                    });

                    // Make sure it's on top of everything with maximum z-index
                    holder.mulaiButton.bringToFront();
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        holder.mulaiButton.setElevation(999999f);
                        holder.mulaiButton.setTranslationZ(999999f);
                    }

                    // Force the parent to redraw to ensure proper layering
                    ((ViewGroup) holder.mulaiButton.getParent()).invalidate();

                    // Tambahkan animasi naik-turun untuk menunjuk ke button
                    holder.mulaiButton.setAlpha(0f);
                    holder.mulaiButton.animate()
                        .alpha(1f)
                        .setDuration(500)
                        .withEndAction(() -> {
                            // Add gentle bouncing up-down animation to point to the button
                            ObjectAnimator bounceAnimator = ObjectAnimator.ofFloat(
                                holder.mulaiButton,
                                "translationY",
                                0f, -8f, 0f, -5f, 0f
                            );
                            bounceAnimator.setDuration(1500); // Reduced from 2000 to 1500 for smoother animation
                            bounceAnimator.setRepeatCount(ValueAnimator.INFINITE);
                            bounceAnimator.setRepeatMode(ValueAnimator.RESTART);

                            // Add a subtle scale animation for extra attention
                            ObjectAnimator scaleAnimator = ObjectAnimator.ofFloat(
                                holder.mulaiButton,
                                "scaleX",
                                1f, 1.03f, 1f
                            );
                            ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(
                                holder.mulaiButton,
                                "scaleY",
                                1f, 1.03f, 1f
                            );
                            scaleAnimator.setDuration(1500); // Match bounce animation duration
                            scaleYAnimator.setDuration(1500); // Match bounce animation duration
                            scaleAnimator.setRepeatCount(ValueAnimator.INFINITE);
                            scaleYAnimator.setRepeatCount(ValueAnimator.INFINITE);
                            scaleAnimator.setRepeatMode(ValueAnimator.RESTART);
                            scaleYAnimator.setRepeatMode(ValueAnimator.RESTART);

                            // Start all animations together
                            AnimatorSet animatorSet = new AnimatorSet();
                            animatorSet.playTogether(bounceAnimator, scaleAnimator, scaleYAnimator);
                            animatorSet.start();
                        })
                        .start();

                    // Add click listener
                    holder.mulaiButton.setOnClickListener(v -> {
                        // Visual feedback
                        v.animate().scaleX(1.2f).scaleY(1.2f).setDuration(200)
                          .withEndAction(() -> v.animate().scaleX(1f).scaleY(1f).setDuration(200).start())
                          .start();

                        // Trigger click action
                        if (onItemClickListener != null) {
                            onItemClickListener.onItemClick(position);
                        }
                    });

                    // Debug
                    Log.d("AdapterChallenge", "Mulai button set visible for position " + position +
                          ", isEven: " + isEven);
                } else {
                    Log.e("AdapterChallenge", "mulaiButton not found in ViewHolder");
                }
            } else if (holder.mulaiButton != null) {
                // Hide mulai button for non-active levels
                holder.mulaiButton.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            Log.e("AdapterChallenge", "Error handling floating Mulai text", e);
        }
    }

    /**
     * Mencari posisi level selanjutnya yang harus diselesaikan user
     * @return posisi level aktif, atau -1 jika semua level sudah selesai
     */
    private int findNextActiveLevel() {
        // Debug: Print all item statuses
        debugItemStatuses();

        for (int i = 0; i < itemList.size(); i++) {
            if (itemList.get(i).getIs_hafal() != 100) {
                Log.d("AdapterChallenge", "Next active level found at position " + i + " (hafal status: " + itemList.get(i).getIs_hafal() + ")");
                return i; // Return posisi level pertama yang belum selesai
            }
        }
        Log.d("AdapterChallenge", "All levels completed - no active level");
        return -1; // Semua level sudah selesai
    }

    /**
     * Debug method to print all item statuses
     */
    private void debugItemStatuses() {
        Log.d("AdapterChallenge", "=== Current Item Statuses ===");
        for (int i = 0; i < itemList.size(); i++) {
            ItemModel item = itemList.get(i);
            Log.d("AdapterChallenge", "Position " + i + ": " + item.getNamaSurat() + " - Hafal: " + item.getIs_hafal());
        }
        Log.d("AdapterChallenge", "=== End Item Statuses ===");
    }

    /**
     * Launch SambungAyat instruction activity for verse slicing
     */
    private void launchSambungAyatActivity(String title, int position) {
        Intent intent = new Intent(context, SambungAyatInstructionActivity.class);
        intent.putExtra("id_juz", ((AudioChallenge) context).getIntent().getStringExtra("id_juz"));

        // Create dynamic title range based on the level
        String titleRange = title.replace("Hari ", "").replace(": ", " ");
        intent.putExtra("title_range", titleRange);

        context.startActivity(intent);
    }

    public void cekHafal(@NonNull AdapterHolder holder, boolean isGenap, int isHafal) {
        int activeDrawable = isGenap ? R.drawable.left_active : R.drawable.right_active;
        int nonActiveDrawable = isGenap ? R.drawable.left_non_active : R.drawable.right_non_active;
        int checkDrawable = R.drawable.ic_check;
        int uncheckDrawable = R.drawable.ic_uncheck_new;
        String hafalText = isGenap ? " Sudah Hafal" : " Sudah Hafal ";
        String tandaiHafalText = isGenap ? " Tandai Hafal" : " Tandai Hafal ";

        if (isHafal == 100) {
            holder.circle.setImageResource(activeDrawable);
            (isGenap ? holder.ic_tandai_hafal_left : holder.ic_tandai_hafal_right).setImageResource(checkDrawable);
            (isGenap ? holder.text_hafal_left : holder.text_hafal_right).setText(hafalText);
        } else {
            holder.circle.setImageResource(nonActiveDrawable);
            (isGenap ? holder.ic_tandai_hafal_left : holder.ic_tandai_hafal_right).setImageResource(uncheckDrawable);
            (isGenap ? holder.text_hafal_left : holder.text_hafal_right).setText(tandaiHafalText);
        }
    }

    private void tandaiHafal(@NonNull AdapterHolder holder, boolean isGenap, AdapterHolder currentHolder, String id_surat, String id_detail_surat) {
        View clickTarget = isGenap ? holder.tandai_hafal_left : holder.tandai_hafal_right;
        int position = holder.getAdapterPosition();
        ItemModel currentItem = itemList.get(position);

        clickTarget.setOnClickListener(view -> {
            if (position > 0 && !isPositionReady(position - 1)) {
                Toast.makeText(context, "Selesaikan dulu hafalan sebelumnya", Toast.LENGTH_SHORT).show();
                return;
            }

            if (currentItem.getIs_hafal() == 100) {
                int lastMarkedPosition = findLastMarkedPosition();
                if (position != lastMarkedPosition) {
                    Toast.makeText(context, "Batalkan terlebih dahulu hafalan setelah ini", Toast.LENGTH_SHORT).show();
                    return;
                }
            }

            // Check for network availability
            if (isNetworkAvailable(context)) {
                // Online implementation - proceed with API call
                apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
                Call<CompleteSurat> call = apiInterface.tandaiHafalRespons(idUser, id_surat, id_detail_surat);
                call.enqueue(new Callback<CompleteSurat>() {
                    @Override
                    public void onResponse(Call<CompleteSurat> call, Response<CompleteSurat> response) {
                        handleHafalResponse(response, currentHolder, isGenap);
                        if (context instanceof AudioChallenge) {
                            ((AudioChallenge) context).refreshPage();
                        }
                    }

                    @Override
                    public void onFailure(Call<CompleteSurat> call, Throwable t) {
                        Log.e("AdapterChallenge", "Error tandaiHafal", t);
                        Toast.makeText(context, t.getLocalizedMessage(), Toast.LENGTH_SHORT).show();
                    }
                });
            } else {
                // Offline implementation - update local database
                handleOfflineHafalUpdate(currentItem, currentHolder, isGenap, id_surat, id_detail_surat);
            }
        });
    }

    private boolean isNetworkAvailable(Context context) {
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            android.net.NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }

    private void handleOfflineHafalUpdate(ItemModel currentItem, AdapterHolder holder, boolean isGenap, String id_surat, String id_detail_surat) {
        int position = holder.getAdapterPosition();

        // Toggle the hafal status locally
        final int newStatus = currentItem.getIs_hafal() == 100 ? 0 : 100;

        executorService.execute(() -> {
            try {
                // Update the entity in the database
                database.hafalanDao().updateHafalStatus(id_surat, idUser, newStatus);

                // Store offline action to sync later when online
                database.offlineActionDao().insert(new OfflineActionEntity(
                        idUser,
                        id_surat,
                        id_detail_surat,
                        newStatus == 100 ? "mark" : "unmark",
                        System.currentTimeMillis()
                ));

                // Update UI on main thread
                ((Activity) context).runOnUiThread(() -> {
                    ((AudioChallenge) context).refreshPage();

                    // Update the item in the adapter
                    currentItem.setIs_hafal(newStatus);
                    notifyItemChanged(position);

                    // Update UI status
                    if (newStatus == 100) {
                        setClickability(holder, position + 1);
                        if (listener != null) {
                            listener.onHafalStatusChanged(true);
                        }
                        Toast.makeText(context, "Hafalan Ditambahkan", Toast.LENGTH_SHORT).show();
                    } else {
                        setClickability(holder, position + 1);
                        if (listener != null) {
                            listener.onHafalStatusChanged(false);
                        }
                        Toast.makeText(context, "Hafalan Dibatalkan", Toast.LENGTH_SHORT).show();
                    }

                    // Refresh semua item untuk memperbarui floating "Mulai" text
                    notifyDataSetChanged();

                    ((Activity) context).runOnUiThread(() -> {
                        if (context instanceof AudioChallenge) {
                        }
                    });
                });
            } catch (Exception e) {
                Log.e("AdapterChallenge", "Error updating offline data", e);
                ((Activity) context).runOnUiThread(() -> {
                    Toast.makeText(context, "Gagal memperbarui data offline: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    private int findLastMarkedPosition() {
        for (int i = itemList.size() - 1; i >= 0; i--) {
            if (itemList.get(i).getIs_hafal() == 100) {
                return i;
            }
        }
        return -1;
    }

    private boolean isPositionReady(int position) {
        if (position <= 0) return true;

        for (int i = 0; i < position; i++) {
            if (itemList.get(i).getIs_hafal() != 100) {
                return false;
            }
        }
        return true;
    }

    private void setClickability(AdapterHolder holder, int position) {
        if (position >= itemList.size()) return;

        boolean isEven = position % 2 == 0;
        View clickTarget = isEven ? holder.tandai_hafal_left : holder.tandai_hafal_right;

        int lastMarkedPosition = findLastMarkedPosition();

        if (position == lastMarkedPosition - 2) {
            clickTarget.setEnabled(true);
            clickTarget.setAlpha(1.0f);
        } else if (position > lastMarkedPosition + 1) {
            clickTarget.setEnabled(false);
            clickTarget.setAlpha(0.5f);
        } else {
            boolean canMark = isPositionReady(position);
            clickTarget.setEnabled(canMark);
            clickTarget.setAlpha(canMark ? 1.0f : 0.5f);
        }
    }

    private void handleHafalResponse(Response<CompleteSurat> response, AdapterHolder holder, boolean isGenap) {
        if (response.isSuccessful() && response.body() != null) {
            CompleteSurat completeSurat = response.body();
            int position = holder.getAdapterPosition();
            if (position >= 0 && position < itemList.size()) {
                if (completeSurat.getData() == 1) {
                    itemList.get(position).setIs_hafal(100);
                    notifyItemChanged(position);
                    setClickability(holder, position + 1);
                    if (listener != null) {
                        listener.onHafalStatusChanged(true);
                    }
                    Toast.makeText(context, "Hafalan Ditambahkan", Toast.LENGTH_SHORT).show();

                    // Refresh semua item untuk memperbarui floating "Mulai" text
                    notifyDataSetChanged();
                } else {
                    itemList.get(position).setIs_hafal(0);
                    notifyItemChanged(position);
                    setClickability(holder, position + 1);
                    if (listener != null) {
                        listener.onHafalStatusChanged(false);
                    }
                    Toast.makeText(context, "Hafalan Dibatalkan", Toast.LENGTH_SHORT).show();

                    // Refresh semua item untuk memperbarui floating "Mulai" text
                    notifyDataSetChanged();
                }
            } else {
                Log.e("AdapterChallenge", "Invalid position: " + position);
            }
        } else {
            Toast.makeText(context, "Gagal memproses hafalan", Toast.LENGTH_SHORT).show();
        }
    }

    // Add method to sync offline actions when online
    public void syncOfflineActions() {
        if (!isNetworkAvailable(context)) {
            return;
        }

        executorService.execute(() -> {
            List<OfflineActionEntity> offlineActions = database.offlineActionDao().getAllPendingActions();

            for (OfflineActionEntity action : offlineActions) {
                try {
                    // Call the API for each pending action
                    apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
                    Call<CompleteSurat> call = apiInterface.tandaiHafalRespons(
                            action.getUserId(),
                            action.getSuratId(),
                            action.getDetailSuratId());

                    // Execute synchronously for offline syncing
                    Response<CompleteSurat> response = call.execute();

                    if (response.isSuccessful() && response.body() != null) {
                        // Delete processed action
                        database.offlineActionDao().delete(action);
                    }
                } catch (Exception e) {
                    Log.e("AdapterChallenge", "Error syncing offline action", e);
                }
            }

            // Refresh UI on main thread after sync
            ((Activity) context).runOnUiThread(() -> {
                if (context instanceof AudioChallenge) {
                    ((AudioChallenge) context).refreshPage();
                }
            });
        });
    }

    private void showDownloadDialog(AdapterHolder holder, ItemModel item, int position) {
        AlertDialog dialog = new AlertDialog.Builder(context)
                .setTitle("Unduh Audio")
                .setMessage("Apakah Anda ingin mengunduh audio untuk " + item.getNamaSurat() + "?")
                .setPositiveButton("Ya", null)
                .setNegativeButton("Tidak", null)
                .setCancelable(true)
                .create();

        dialog.show();

        Button positiveButton = dialog.getButton(AlertDialog.BUTTON_POSITIVE);
        Button negativeButton = dialog.getButton(AlertDialog.BUTTON_NEGATIVE);

        if (positiveButton != null) {
            positiveButton.setTextColor(ContextCompat.getColor(context, R.color.orangemuda));
        }
        if (negativeButton != null) {
            negativeButton.setTextColor(ContextCompat.getColor(context, R.color.orangemuda));
        }

        positiveButton.setOnClickListener(v1 -> {
            ProgressDialog progressDialog = new ProgressDialog(context);
            progressDialog.setMessage("Mengunduh audio...");
            progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
            progressDialog.setProgress(0);
            progressDialog.setIndeterminateDrawable(new ColorDrawable(ContextCompat.getColor(context, R.color.orangemuda)));
            progressDialog.setCancelable(false);
            progressDialog.show();

            executor.execute(() -> {
                try {
                    String fileName = "audio_" + item.getId_detail_surat() + ".mp3";
                    downloadAudio(context, item.getUrl_audio(), fileName, holder, item, position, progressDialog);
                } catch (Exception e) {
                    mainHandler.post(() -> {
                        progressDialog.dismiss();
                        Toast.makeText(context, "Error downloading audio: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    });
                }
            });
            dialog.dismiss();
        });

        negativeButton.setOnClickListener(v2 -> {
            ArrayList<String> localPaths = new ArrayList<>();
            localPaths.add(item.getUrl_audio());

            launchPlayActivity(holder, item, position, item.getUrl_audio());
            dialog.dismiss();
        });
    }

    private void downloadAudio(Context context, String url, String fileName,
                               AdapterHolder holder, ItemModel item, int position,
                               ProgressDialog progressDialog) {
        File directory = new File(context.getFilesDir(), "audio");
        if (!directory.exists()) {
            directory.mkdirs();
        }

        File outputFile = new File(directory, fileName);

        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url(url).build();

        client.newCall(request).enqueue(new okhttp3.Callback() {
            @Override
            public void onFailure(@NonNull okhttp3.Call call, @NonNull IOException e) {
                mainHandler.post(() -> {
                    progressDialog.dismiss();
                    Toast.makeText(context, "Download Gagal: Periksa koneksi internet anda", Toast.LENGTH_SHORT).show();
                });
            }

            @Override
            public void onResponse(@NonNull okhttp3.Call call, @NonNull okhttp3.Response response) {
                if (!response.isSuccessful()) {
                    mainHandler.post(() -> {
                        progressDialog.dismiss();
                        Log.e("DownloadError", "Download failed: " + response.code());
                    });
                    return;
                }

                try (InputStream inputStream = response.body().byteStream();
                     OutputStream outputStream = new FileOutputStream(outputFile)) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    outputStream.flush();

                    executor.execute(() -> {
                        try {
                            AudioFilesEntity newAudioFile = new AudioFilesEntity(
                                    item.getId_detail_surat(), url);
                            newAudioFile.setLocalPath(outputFile.getAbsolutePath());
                            newAudioFile.setDownloaded(true);
                            suratDetailDatabase.audioFileDao().insert(newAudioFile);

                            mainHandler.post(() -> {
                                progressDialog.dismiss();
                                launchPlayActivity(holder, item, position, outputFile.getAbsolutePath());

                            });
                        } catch (Exception e) {
                            mainHandler.post(() -> {
                                progressDialog.dismiss();
                                Toast.makeText(context, "Error saving audio: " + e.getMessage(),
                                        Toast.LENGTH_SHORT).show();
                            });
                        }
                    });
                } catch (IOException e) {
                    mainHandler.post(() -> {
                        progressDialog.dismiss();
                        Toast.makeText(context, "Error downloading audio: " + e.getMessage(),
                                Toast.LENGTH_SHORT).show();
                    });
                }
            }
        });
    }

    private void launchPlayActivity(AdapterHolder holder, ItemModel item, int position, String localPath) {
        File audioFile = new File(localPath);

        if (!audioFile.exists()) {
            localPath = item.getUrl_audio();
            Toast.makeText(context, "Local video not found. Using original URL.", Toast.LENGTH_SHORT).show();
        }

        Intent intent = new Intent(context, Play.class);
        intent.putExtra("id_juz", ((AudioChallenge) context).getIntent().getStringExtra("id_juz"));
        intent.putExtra("id_surat", item.getIdSurat());
        intent.putExtra("halaman", item.getHalaman());
        intent.putExtra("id_detail_surat", item.getId_detail_surat());
        intent.putExtra("img", item.getGambar());
        intent.putExtra("nama_surat", item.getNamaSurat());
        intent.putExtra("nama_juz", namaJuz);
        intent.putExtra("jumlah_ayat", item.getJumlah_ayat_potong());
        intent.putExtra("arti_surat", item.getArtiSurat());
        intent.putExtra("url_audio", localPath);
        intent.putExtra("complete_surat", holder.complete);
        intent.putExtra("arrayListNamaSurat", arrayListNamaSurat);
        intent.putExtra("arrayListUrl", arrayListUrl);
        intent.putExtra("from", "audio_challenge");
        intent.putExtra("posisi", position);
        intent.putExtra("posisiMushaf", ((AudioChallenge) context).getIntent().getStringExtra("posisiMushaf"));
        intent.putExtra("id_user", idUser);

        intent.putExtra("from_detail_surat", false);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        context.startActivity(intent);
//        ((AudioChallenge) context).finish();

        mainHandler.post(() ->
                Toast.makeText(context, "Playing surat " + item.getNamaSurat() + " " + item.getJumlah_ayat_potong(),
                        Toast.LENGTH_LONG).show()
        );
    }

    @Override
    public int getItemCount() {
        return itemList.size();
    }

    public static class AdapterHolder extends RecyclerView.ViewHolder {
        ImageView btn, btn2;
        TextView urutanAudio, urutanAudio2, ayat, ayat2, text_hafal_left, text_hafal_right;
        ImageView circle, ic_tandai_hafal_left, ic_tandai_hafal_right;
        ImageView mulaiButton; // Button for "Mulai" text
        LinearLayout layout_left, layout_right, tandai_hafal_left, tandai_hafal_right;
        String complete;

        public AdapterHolder(@NonNull View itemView) {
            super(itemView);
            btn = itemView.findViewById(R.id.btn_surat);
            btn2 = itemView.findViewById(R.id.btn_surat2);
            circle = itemView.findViewById(R.id.circle);
            layout_right = itemView.findViewById(R.id.layout_right);
            layout_left = itemView.findViewById(R.id.layout_left);
            urutanAudio = itemView.findViewById(R.id.urutan_audio);
            urutanAudio2 = itemView.findViewById(R.id.urutan_audio2);
            ayat = itemView.findViewById(R.id.ayat);
            ayat2 = itemView.findViewById(R.id.ayat2);
            text_hafal_left = itemView.findViewById(R.id.text_hafal_left);
            text_hafal_right = itemView.findViewById(R.id.text_hafal_right);
            ic_tandai_hafal_left = itemView.findViewById(R.id.ic_tandai_hafal_left);
            ic_tandai_hafal_right = itemView.findViewById(R.id.ic_tandai_hafal_right);
            tandai_hafal_left = itemView.findViewById(R.id.tandai_hafal_left);
            tandai_hafal_right = itemView.findViewById(R.id.tandai_hafal_right);

            // Initialize the mulai button
            mulaiButton = itemView.findViewById(R.id.mulai_button);

            complete = "0";
        }
    }
}